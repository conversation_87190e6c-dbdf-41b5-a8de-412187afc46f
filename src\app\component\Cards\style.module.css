.home_card {
    width: 20rem;
    height: 10rem;
    padding: 1rem;
    margin: 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-evenly;
    border-radius: 4px;
    cursor: pointer;
    transition: 0.3s;
}

.home_card:active {
    background-color: var(--pc5);
}

.home_card:hover {
    box-shadow: 0px 0px 3px 1px var(--pc1);
    transform: scale(1.1);
}

.cardContain {
    border-radius: 8px;
    position: relative;
    padding: 12px;
    width: 18%;
    /* height: 80px; */
    overflow: hidden;
}

.index {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    font-weight: bold;
}

.emptyRing {
    position: absolute;
    top: -60%;
    right: 10%;
    border-radius: 50%;
    width: 100px;
    height: 100px;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.filledRing {
    position: absolute;
    top: -45%;
    right: -15%;
    border-radius: 50%;
    width: 100px;
    height: 100px;
    background-color: rgba(255, 255, 255, 0.2);
}

.title {
    font-weight: 600;
    color: white;
    margin: 8px 0px;
    font-size: 13px;
}

@media screen and (max-width:426px) {
    .home_card {
        margin: 1rem 0.5rem;
    }
}

@media screen and (max-width:376px) {
    .home_card {
        width: 80vw;
        margin: 1rem 0.5rem;
    }
}


@media (max-width: 768px) {
    .cardContain {
        width: 45%;
    }
}

@media (max-width: 480px) {
    .cardContain {
        width: 100%;
    }
}

.gap {
    border: 2px solid gray;
    width: 16px;
    height: 16px;
    padding: 1px;
}