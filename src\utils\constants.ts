export const fileTypes = [
 'JPG',
 'PNG',
 'GIF',
 'PDF',
 'DOCX',
 'XLSX',
 'PPTX',
 'TXT',
 'ZIP',
 'MP4',
]
export const assessmentMethod = [
 { value: 'Obs', title: 'Observations' },
 { value: 'PA', title: 'Practical assessment' },
 { value: 'ET', title: 'Exams and Tests' },
 { value: 'PD', title: 'Professional discussion' },
 { value: 'I', title: 'Interview' },
 { value: 'Q&A', title: 'Question and Answers' },
 { value: 'P', title: 'Project' },
 { value: 'RA', title: 'Reflective Account' },
 { value: 'WT', title: 'Witness Testimony' },
 { value: 'PE', title: 'Product Evidence' },
 { value: 'SI', title: 'Simulation' },
 { value: 'OT', title: 'Other' },
 { value: 'RPL', title: 'Recognized prior learning' },
]
export const sessions = [
 {
  id: 'session1',
  label: '9 June 2025, 9:00 AM - 11:00 AM',
 },
 {
  id: 'session2',
  label: '10 June 2025, 2:00 PM - 4:00 PM',
 },
 {
  id: 'session3',
  label: '12 June 2025, 1:00 PM - 3:00 PM',
 },
]