# Required Dependencies for CreateAndUploadDocument Component

To use the CreateAndUploadDocument component, you need to install the following dependencies:

## Install Commands

```bash
# Core document generation libraries
npm install docx xlsx pptxgenjs

# Rich text editor
npm install react-quill

# Type definitions (if using TypeScript)
npm install @types/react-quill --save-dev
```

## Dependencies Explanation

1. **docx** - For generating Word documents (.docx files)
2. **xlsx** (SheetJS) - For generating Excel spreadsheets (.xlsx files)  
3. **pptxgenjs** - For generating PowerPoint presentations (.pptx files)
4. **react-quill** - Rich text editor for Word document content

## CSS Import Required

Make sure to import the Quill CSS in your component or globally:

```css
@import 'react-quill/dist/quill.snow.css';
```

## API Endpoint

The component expects an API endpoint at:
- **POST** `/api/v1/assignment/create`
- **FormData key**: `file`
- **Headers**: `Authorization: Bearer ${token}`

## Usage Example

```tsx
import CreateAndUploadDocument from 'src/app/component/Documents/CreateAndUploadDocument';

function MyPage() {
  return (
    <div>
      <h1>Document Creator</h1>
      <CreateAndUploadDocument />
    </div>
  );
}
```

## Features

### Word Editor
- Rich text editing with ReactQuill
- Document title input
- Generates .docx files using the `docx` library
- Converts rich text to Word format

### Excel Editor  
- Dynamic table with add/remove rows
- Editable cells
- Custom sheet naming
- Generates .xlsx files using SheetJS

### PowerPoint Editor
- Multiple slide management
- Title and content for each slide
- Add/remove slides functionality
- Generates .pptx files using PptxGenJS

### Upload Functionality
- Automatic file upload after generation
- Progress indicators
- Error handling with user feedback
- JWT token authentication support
