export let appData: Object[] = [
    {
      Id: 1,
      Subject: 'Explosion of Betelgeuse Star',
      StartTime: new Date(2018, 1, 11, 9, 30),
      EndTime: new Date(2018, 1, 11, 11, 0),
      CategoryColor: '#1aaa55',
    },
    {
      Id: 2,
      Subject: 'Thule Air Crash Report',
      StartTime: new Date(2018, 1, 12, 12, 0),
      EndTime: new Date(2018, 1, 12, 14, 0),
      CategoryColor: '#357cd2',
    },
    {
      Id: 3,
      Subject: 'Blue Moon Eclipse',
      StartTime: new Date(2018, 1, 13, 9, 30),
      EndTime: new Date(2018, 1, 13, 11, 0),
      CategoryColor: '#7fa900',
    },
    {
      Id: 4,
      Subject: 'Meteor Showers in 2018',
      StartTime: new Date(2018, 1, 14, 13, 0),
      EndTime: new Date(2018, 1, 14, 14, 30),
      CategoryColor: '#ea7a57',
    },
    {
      Id: 5,
      Subject: 'Milky Way as Melting pot',
      StartTime: new Date(2018, 1, 15, 12, 0),
      EndTime: new Date(2018, 1, 15, 14, 0),
      CategoryColor: '#00bdae',
    },
    {
      Id: 6,
      Subject: 'Mysteries of Bermuda Triangle',
      StartTime: new Date(2018, 1, 15, 9, 30),
      EndTime: new Date(2018, 1, 15, 11, 0),
      CategoryColor: '#f57f17',
    },
    {
      Id: 7,
      Subject: 'Glaciers and Snowflakes',
      StartTime: new Date(2018, 1, 16, 11, 0),
      EndTime: new Date(2018, 1, 16, 12, 30),
      CategoryColor: '#1aaa55',
    },
    {
      Id: 8,
      Subject: 'Life on Mars',
      StartTime: new Date(2018, 1, 17, 9, 0),
      EndTime: new Date(2018, 1, 17, 10, 0),
      CategoryColor: '#357cd2',
    },
    {
      Id: 9,
      Subject: 'Alien Civilization',
      StartTime: new Date(2018, 1, 19, 11, 0),
      EndTime: new Date(2018, 1, 19, 13, 0),
      CategoryColor: '#7fa900',
    },
    {
      Id: 10,
      Subject: 'Wildlife Galleries',
      StartTime: new Date(2018, 1, 21, 11, 0),
      EndTime: new Date(2018, 1, 21, 13, 0),
      CategoryColor: '#ea7a57',
    },
    {
      Id: 11,
      Subject: 'Best Photography 2018',
      StartTime: new Date(2018, 1, 22, 9, 30),
      EndTime: new Date(2018, 1, 22, 11, 0),
      CategoryColor: '#00bdae',
    },
    {
      Id: 12,
      Subject: 'Smarter Puppies',
      StartTime: new Date(2018, 1, 9, 10, 0),
      EndTime: new Date(2018, 1, 9, 11, 30),
      CategoryColor: '#f57f17',
    },
    {
      Id: 13,
      Subject: 'Myths of Andromeda Galaxy',
      StartTime: new Date(2018, 1, 7, 10, 30),
      EndTime: new Date(2018, 1, 7, 12, 30),
      CategoryColor: '#1aaa55',
    },
    {
      Id: 14,
      Subject: 'Aliens vs Humans',
      StartTime: new Date(2018, 1, 3, 8, 0),
      EndTime: new Date(2018, 1, 3, 11, 30),
      CategoryColor: '#357cd2',
    },
    {
      Id: 15,
      Subject: 'Facts of Humming Birds',
      StartTime: new Date(2018, 1, 20, 9, 30),
      EndTime: new Date(2018, 1, 20, 11, 0),
      CategoryColor: '#7fa900',
    },
    {
      Id: 16,
      Subject: 'Sky Gazers',
      StartTime: new Date(2018, 1, 23, 11, 0),
      EndTime: new Date(2018, 1, 23, 13, 0),
      CategoryColor: '#ea7a57',
    },
    {
      Id: 17,
      Subject: 'The Cycle of Seasons',
      StartTime: new Date(2018, 1, 12, 5, 30),
      EndTime: new Date(2018, 1, 12, 7, 30),
      CategoryColor: '#00bdae',
    },
    {
      Id: 18,
      Subject: 'Space Galaxies and Planets',
      StartTime: new Date(2018, 1, 12, 17, 0),
      EndTime: new Date(2018, 1, 12, 18, 30),
      CategoryColor: '#f57f17',
    },
    {
      Id: 19,
      Subject: 'Lifecycle of Bumblebee',
      StartTime: new Date(2018, 1, 15, 6, 0),
      EndTime: new Date(2018, 1, 15, 7, 30),
      CategoryColor: '#7fa900',
    },
    {
      Id: 20,
      Subject: 'Sky Gazers',
      StartTime: new Date(2018, 1, 15, 16, 0),
      EndTime: new Date(2018, 1, 15, 18, 0),
      CategoryColor: '#ea7a57',
    },
  ];