# See https://help.github.com/ignore-files/ for more about ignoring files.

# dependencies
/node_modules
/.yarn

# testing
/coverage

# production
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
.eslintcache

/.idea
/.vscode

/src/styles/tailwind.css

npm-debug.log*
yarn-debug.log*
yarn-error.log*

.yarn/*
!.yarn/patches
!.yarn/releases
!.yarn/plugins
!.yarn/sdks
!.yarn/versions
.pnp.*
package-lock.json