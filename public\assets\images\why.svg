<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 27.3.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 360 315" style="enable-background:new 0 0 360 315;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#527E90;}
	.st1{fill:#6D81A3;}
	.st2{fill:#7D92A5;}
	.st3{fill:#FFFFFF;}
	.st4{fill:#FFAA9D;}
	.st5{fill:#000305;}
	.st6{fill:#5B718F;}
	.st7{fill:#FF9F91;}
	.st8{fill:#709FA3;}
	.st9{fill:#F1F5FD;}
	.st10{font-family:'ArialRoundedMTBold';}
	.st11{font-size:44.1376px;}
	.st12{font-size:51.7714px;}
	.st13{font-size:22.5794px;}
	.st14{font-size:10.6538px;}
</style>
<path class="st0" d="M355.9,232.1c7.9-38.7,5-74.4-15.1-108.4c-14.4-24.2-39.3-44.5-67.8-45c-26.9-0.5-51.1,16.1-77.7,20.3
	c-43.7,6.9-89-20.2-131.4-7.8c-22.7,6.6-41.2,24.3-51.7,45.2c-10.4,21-13.4,44.9-11.7,68.2C3.4,245.9,22,287,53.6,313.8h257.7
	C332.8,288.9,350.8,257.1,355.9,232.1z"/>
<path class="st1" d="M289.4,311.8c0,1.3-48.1,2.4-107.4,2.4s-107.4-1.1-107.4-2.4s48.1-2.4,107.4-2.4S289.4,310.5,289.4,311.8z"/>
<path class="st2" d="M206.5,265.7h-48.2v38.4h48.2V265.7z"/>
<path class="st3" d="M288.7,264.5H74.4c-11.5,0-20.8-9.3-20.8-20.8v-123c0-11.5,9.3-20.8,20.8-20.8h214.3c11.5,0,20.8,9.3,20.8,20.8
	v123C309.6,255.2,300.2,264.5,288.7,264.5z"/>
<path class="st2" d="M288.7,266.7H74.4c-12.7,0-23-10.3-23-23v-123c0-12.7,10.3-23,23-23h214.3c12.7,0,23,10.3,23,23v123
	C311.8,256.3,301.4,266.7,288.7,266.7z M74.4,102.1c-10.3,0-18.6,8.4-18.6,18.6v123c0,10.3,8.4,18.6,18.6,18.6h214.3
	c10.3,0,18.6-8.4,18.6-18.6v-123c0-10.3-8.4-18.6-18.6-18.6L74.4,102.1L74.4,102.1z"/>
<path class="st2" d="M246.6,311.9H114c-2.1,0-3.9-1.7-3.9-3.9c0-2.1,1.7-3.9,3.9-3.9h132.6c2.1,0,3.9,1.7,3.9,3.9
	C250.5,310.2,248.8,311.9,246.6,311.9z"/>
<path class="st2" d="M253.6,308.6h6.8v-36.3V269v-25.9h3.3V269h5.2v-25.9h3.3V269v3.3H269h-5.2v36.3h6.8c0.9,0,1.7,0.7,1.7,1.7
	c0,0.9-0.7,1.7-1.7,1.7h-6.8h-3.3h-6.8c-0.9,0-1.7-0.7-1.7-1.7C251.9,309.4,252.7,308.6,253.6,308.6z"/>
<path class="st2" d="M265.9,228.9c14.5,0,26.2,2.7,26.2,6.1v3.3c0,3.4-11.8,6.1-26.2,6.1c-14.5,0-26.2-2.7-26.2-6.1V235
	C239.7,231.6,251.4,228.9,265.9,228.9z"/>
<path class="st4" d="M238,192.3c0,0-7.7-4.8-10.1-7.3c-2.3-2.5-3.3-3.6-2.7-4.1s7.1,2.4,7.8,1.9c0.6-0.5,0.1-1.7,0.8-2.2
	c0.7-0.6,4.9,3.8,5.9,3.5C240.7,183.7,239.2,191.6,238,192.3z"/>
<path class="st4" d="M207.1,281.3l-5.7,5.5L187,273.5l4.7-7.7L207.1,281.3z"/>
<path class="st5" d="M202.3,286c0,0-3.9,5-4.7,5c-0.7,0-17.4-18-17.3-18.6c0.1-0.6,7.4,0,7.4,0L202.3,286z"/>
<path class="st6" d="M190.9,265c0,0,16.7,17.4,17.3,17.4s80.1-61,80.1-61l-33.7-14.9C254.6,206.5,211.8,218.2,190.9,265z"/>
<path class="st4" d="M266.3,270.2l2.8,7.4l-17.9,8l-5.2-7.4L266.3,270.2z"/>
<path class="st6" d="M252,206.5c0,0-28.1,5.9-30.6,15.4c-2.4,9.5-0.2,19.9,7.5,32.1s15.6,25.4,17.3,25.8c0,0,20.8-8.3,21.3-9.4
	c0.5-1-11.7-29.7-11.7-29.7s24.7-1.7,30.5-16.2L252,206.5z"/>
<path class="st1" d="M250,206.4c-0.2,1.7,30.5,20.9,35.9,20s12.5-23.4,10.5-40.1s-17-29.3-23.8-28.7s-10.5,7-11.4,17.2
	C259.8,189.7,250.3,204.5,250,206.4z"/>
<path class="st0" d="M275.7,160.1c3.6-0.3,16.1,7,19,28.5c2.9,21.6-9,23.4-19,21.9c-10-1.4-35.2-13.7-38.8-18.2c0,0-0.8-6.9,1.9-9.4
	c0,0,24.3,2.5,30.6,4.3C275.6,189.3,261.9,161.3,275.7,160.1z"/>
<path class="st5" d="M268.7,276.5c0,0,3,5.6,2.7,6.3c-0.3,0.7-23.4,8.9-23.8,8.6c-0.5-0.3,2.9-6.8,2.9-6.8L268.7,276.5z"/>
<path class="st4" d="M255.4,146.1l-0.1,7.7h1.2c0,0,0.9,6.4,5.4,5.4c1.3-0.3,2.9-1.4,3.4-1.2c0.6,0.2,0.8,2,0.9,2.5
	c0.5,1.9,7.3,0,6.2-2c-1-1.9-2.6-4.4-2.3-5.1c1.1-2.5,1.5-8.6-4.7-10.7C260.4,141,255.4,143.4,255.4,146.1z"/>
<path class="st7" d="M272.3,158.4c-1-1.9-2.6-4.4-2.3-5.1c0,0-2.6,4-5,4.6c0.1,0,0.2,0,0.2,0c0.6,0.2,0.8,2,0.9,2.5
	C266.7,162.4,273.4,160.4,272.3,158.4z"/>
<path class="st5" d="M265.8,149.8c2.9-0.5,1.1,6,4.3,5.8c3.2-0.2,5.4-7.2,3.1-12.6c-2.4-5.4-6.8-5.1-11.1-4.3s-9.5,2.7-9.5,5.9
	c0,7.7,9.2,1.3,9.8,3.6c0.5,1.8,1.2,7-0.7,7.1c-1.9,0.1-3.8-1.4-5.2-1c-0.9,0.2-3.2,10.4,2.9,10.7c6,0.3,7.6-5,6.3-9.3
	C264.5,151.4,264.3,150.1,265.8,149.8z"/>
<path class="st1" d="M103,311.4c0,1.3-7.1,2.4-15.9,2.4s-15.9-1-15.9-2.4s7.1-2.4,15.9-2.4S103,310,103,311.4z"/>
<path class="st8" d="M65.6,240.3c-0.6,2.8,2.8,3.3,3.3,7.2c0.6,3.9-1.1,5-0.6,7.2c0.6,2.2,3.9,2.8,4.4,8.4c0.6,5.6-1.7,5-1.1,8.4
	c0.6,3.3,3.9,3.3,4.4,6.1c0.6,2.8,0.6,6.7,1.7,10s4.4,8.4,6.1,8.4s6.1-9.5,5-12.2c-1.1-2.8-2.8-3.3-2.8-5.6c0-2.2,2.2-7.8,1.7-11.7
	c-0.6-3.9-5.6-5.6-6.1-8.4c-0.6-2.8,1.1-10-2.8-13.3C75,241.4,66.1,237.6,65.6,240.3z"/>
<path class="st9" d="M83.7,295c-0.2,0-0.3-0.1-0.3-0.3c0-0.1-0.1-7.3-2.2-9.4s-3.4-8.7-4-21.3c-0.6-11.4-8.8-20.9-8.8-21
	c-0.1-0.1-0.1-0.3,0-0.4c0.1-0.1,0.3-0.1,0.4,0s8.4,9.7,9,21.3c0.6,12.3,1.8,18.9,3.8,21c2.3,2.3,2.3,9.5,2.3,9.8
	C84,294.9,83.8,295,83.7,295z"/>
<path class="st8" d="M116.5,248.8c2,2-4.4,2.8-5,6.7s-0.6,5.6-2.8,7.8c-2.2,2.2-5.6,2.8-5,6.1c0.6,3.3,1.7,6.7,0,8.4
	c-1.7,1.7-5.6,1.7-6.7,4.4c-1.1,2.8-0.6,6.1-2.8,8.9c-2.2,2.8-5.6,8.4-8.4,7.2c-2.8-1.1-1.1-13.9,0-16.1s2.2-3.9,2.2-5.6
	s-0.6-4.4,0.6-7.2c1.1-2.8,7.2-3.9,7.8-5.6c0.6-1.7,1.1-10.6,4.4-12.8C104.3,248.8,113.2,245.4,116.5,248.8z"/>
<path class="st3" d="M86.8,297.3c-0.1,0-0.1,0-0.2,0c-0.1-0.1-0.2-0.2-0.1-0.4c0,0,2.3-3.8,2.7-5.7c0.1-0.5,0.3-1.2,0.4-2.1
	c0.6-3.1,1.5-7.7,3-9.2c2.1-2.1,5.4-8.4,7.7-16.6c2.9-10,12.3-13,12.4-13c0.2,0,0.3,0,0.3,0.2s0,0.3-0.2,0.3
	c-0.1,0-9.3,2.9-12.1,12.6c-2.3,8.2-5.7,14.7-7.8,16.8c-1.3,1.3-2.3,6.1-2.8,8.9c-0.2,0.9-0.3,1.6-0.4,2.1c-0.5,2-2.7,5.6-2.8,5.8
	C87,297.3,86.9,297.3,86.8,297.3z"/>
<path class="st0" d="M86.1,255.1c-6.7,0.6-4.4,20.6-4.4,20.6c1.7,17.3,3.9,25,5.6,25.6s5-24.5,5-30.6
	C92.2,270.7,92.8,254.6,86.1,255.1z"/>
<path class="st3" d="M86.9,298.6L86.9,298.6c-0.2,0-0.3-0.2-0.3-0.3c2.2-18.1-0.5-40.9-0.6-41.1c0-0.2,0.1-0.3,0.2-0.3
	c0.2,0,0.3,0.1,0.3,0.2c0,0.2,2.8,23.1,0.6,41.3C87.2,298.4,87,298.6,86.9,298.6z"/>
<path class="st1" d="M95.2,311.2h-17c-0.6,0-1-0.3-1.1-0.8l-2.4-17.6c-0.1-0.8,0.5-1.5,1.3-1.5h21.5c0.8,0,1.4,0.7,1.3,1.5
	l-2.4,17.6C96.2,310.9,95.8,311.2,95.2,311.2z"/>
<path class="st1" d="M93.3,78.6H22.1c-4.7,0-8.9-1.9-11.9-4.9c-3-3.1-4.9-7.3-4.9-11.9c0-3.3,0.9-6.4,2.6-9c3-4.7,8.3-7.8,14.3-7.8
	h71.2c4.7,0,8.9,1.9,11.9,4.9c3.1,3.1,4.9,7.3,4.9,11.9c0,4.3-1.7,8.3-4.3,11.3C102.7,76.5,98.3,78.6,93.3,78.6z"/>
<path class="st1" d="M51.6,75.9c14.1,0,25.6-11.4,25.6-25.6S65.7,24.8,51.6,24.8C37.4,24.8,26,36.2,26,50.4S37.4,75.9,51.6,75.9z"/>
<path class="st1" d="M270.3,40.3h-48.6c-2.8,0-5.4-1-7.4-2.7c-2.5-2.1-4-5.2-4-8.8c0-3.2,1.3-6.1,3.4-8.1c2.1-2.1,5-3.4,8.1-3.4
	h48.5c3.2,0,6.1,1.3,8.1,3.4c2.1,2.1,3.4,5,3.4,8.1c0,3.2-1.3,6.1-3.4,8.1C276.4,39,273.5,40.3,270.3,40.3z"/>
<path class="st1" d="M258.8,25c2.2-9.4-3.5-18.8-12.9-21s-18.8,3.5-21,12.9s3.5,18.8,12.9,21C247.2,40.2,256.6,34.4,258.8,25z"/>
<circle class="st2" cx="71.5" cy="115.5" r="2.5"/>
<circle class="st2" cx="81.4" cy="115.5" r="2.5"/>
<circle class="st2" cx="91.4" cy="115.5" r="2.5"/>
<text transform="matrix(1.0307 0 0 1 81.2613 163.1572)" class="st1 st10 st11">Why</text>
<text transform="matrix(1.0212 0 0 1 82.5519 217.7072)" class="st1 st10 st12">Loker</text>
<g>
	<text transform="matrix(1.6123 0.6431 -0.3705 0.9288 252.7766 128.5859)" class="st6 st10 st13">?</text>
</g>
<g>
	<text transform="matrix(1.5033 -1.2729 0.6462 0.7632 246.5653 135.7679)" class="st6 st10 st14">?</text>
</g>
</svg>
