@media (max-width: 768px) {
    .hide_on_small {
        display: none;
    }
}

@media (max-width: 426px) {
    .top_right_image {
        width: 55%;
    }
    .top_left_image {
        width: 80%;
        margin-top: 50px;
    }
}

@media screen and (min-device-width: 1024px) and (max-device-width: 1024px) {
    .top_left_image {
        width: 80%;
    }
}

@media screen and (min-device-width: 2560px) and (max-device-width: 2560px) {
    .top_right_image {
        width: 50%;
    }
    .top_left_image {
        top: 80px;
        left: 80px;
        width: 80%;
    }
    .bottom_center_image {
        width: 80%;
    }
}