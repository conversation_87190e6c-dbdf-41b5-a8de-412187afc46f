<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 27.3.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 360 315" style="enable-background:new 0 0 360 315;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#6D81A3;}
	.st1{fill:#7D92A5;}
	.st2{fill:#FFFFFF;}
	.st3{fill:#FFAA9D;}
	.st4{fill:#000305;}
	.st5{fill:#5B718F;}
	.st6{fill:#527E90;}
	.st7{fill:#FF9F91;}
	.st8{fill:#709FA3;}
	.st9{fill:#F1F5FD;}
	.st10{font-family:'ArialRoundedMTBold';}
	.st11{font-size:20.3978px;}
	.st12{font-size:13.4108px;}
	.st13{font-size:168.2262px;}
</style>
<path class="st0" d="M45.1,289.2c0,1.6,60.5,3,135.2,3s135.2-1.4,135.2-3s-60.5-3-135.2-3S45.1,287.5,45.1,289.2z"/>
<path class="st1" d="M149.5,231.2h60.7v48.3h-60.7V231.2z"/>
<path class="st2" d="M46,229.6h269.7c14.5,0,26.2-11.7,26.2-26.2V48.6c0-14.5-11.7-26.2-26.2-26.2H46c-14.5,0-26.2,11.7-26.2,26.2
	v154.8C19.7,217.9,31.5,229.6,46,229.6z"/>
<path class="st1" d="M46,232.4h269.7c16,0,29-13,29-29V48.6c0-16-13-29-29-29H46c-16,0-29,13-29,29v154.8
	C16.9,219.3,30,232.4,46,232.4z M315.7,25.2c13,0,23.4,10.6,23.4,23.4v154.8c0,13-10.6,23.4-23.4,23.4H46c-13,0-23.4-10.6-23.4-23.4
	V48.6c0-13,10.6-23.4,23.4-23.4L315.7,25.2L315.7,25.2z"/>
<path class="st1" d="M99,289.3h166.9c2.6,0,4.9-2.1,4.9-4.9c0-2.6-2.1-4.9-4.9-4.9H99c-2.6,0-4.9,2.1-4.9,4.9
	C94.1,287.2,96.2,289.3,99,289.3z"/>
<path class="st1" d="M90.2,285.2h-8.6v-45.7v-4.2v-32.6h-4.2v32.6h-6.5v-32.6h-4.2v32.6v4.2h4h6.5v45.7h-8.6c-1.1,0-2.1,0.9-2.1,2.1
	c0,1.1,0.9,2.1,2.1,2.1h8.6h4.2h8.6c1.1,0,2.1-0.9,2.1-2.1C92.3,286.2,91.3,285.2,90.2,285.2z"/>
<path class="st1" d="M74.7,184.8c-18.3,0-33,3.4-33,7.7v4.2c0,4.3,14.9,7.7,33,7.7c18.3,0,33-3.4,33-7.7v-4.2
	C107.7,188.2,93,184.8,74.7,184.8z"/>
<path class="st3" d="M109.8,138.8c0,0,9.7-6,12.7-9.2c2.9-3.1,4.2-4.5,3.4-5.2c-0.8-0.6-8.9,3-9.8,2.4c-0.8-0.6-0.1-2.1-1-2.8
	c-0.9-0.8-6.2,4.8-7.4,4.4C106.4,127.9,108.3,137.9,109.8,138.8z"/>
<path class="st3" d="M148.7,250.8l7.2,6.9L174,241l-5.9-9.7L148.7,250.8z"/>
<path class="st4" d="M154.8,256.7c0,0,4.9,6.3,5.9,6.3c0.9,0,21.9-22.7,21.8-23.4c-0.1-0.8-9.3,0-9.3,0L154.8,256.7z"/>
<path class="st5" d="M169.1,230.3c0,0-21,21.9-21.8,21.9c-0.8,0-100.8-76.8-100.8-76.8l42.4-18.8
	C88.9,156.6,142.8,171.4,169.1,230.3z"/>
<path class="st3" d="M74.2,236.8l-3.5,9.3l22.5,10.1l6.5-9.3L74.2,236.8z"/>
<path class="st5" d="M92.2,156.6c0,0,35.4,7.4,38.5,19.4c3,12,0.3,25-9.4,40.4s-19.6,32-21.8,32.5c0,0-26.2-10.4-26.8-11.8
	c-0.6-1.3,14.7-37.4,14.7-37.4s-31.1-2.1-38.4-20.4L92.2,156.6z"/>
<path class="st0" d="M94.7,156.5c0.3,2.1-38.4,26.3-45.2,25.2c-6.8-1.1-15.7-29.5-13.2-50.5s21.4-36.9,30-36.1s13.2,8.8,14.3,21.7
	C82.4,135.5,94.3,154.1,94.7,156.5z"/>
<path class="st6" d="M62.4,98.2c-4.5-0.4-20.3,8.8-23.9,35.9c-3.7,27.2,11.3,29.5,23.9,27.6c12.6-1.8,44.3-17.2,48.8-22.9
	c0,0,1-8.7-2.4-11.8c0,0-30.6,3.1-38.5,5.4C62.5,135,79.7,99.7,62.4,98.2z"/>
<path class="st4" d="M71.2,244.7c0,0-3.8,7-3.4,7.9c0.4,0.9,29.5,11.2,30,10.8c0.6-0.4-3.7-8.6-3.7-8.6L71.2,244.7z"/>
<path class="st3" d="M87.9,80.6l0.1,9.7h-1.5c0,0-1.1,8.1-6.8,6.8c-1.6-0.4-3.7-1.8-4.3-1.5c-0.8,0.3-1,2.5-1.1,3.1
	c-0.6,2.4-9.2,0-7.8-2.5c1.3-2.4,3.3-5.5,2.9-6.4C68,86.6,67.5,79,75.3,76.3C81.6,74.2,87.9,77.2,87.9,80.6z"/>
<path class="st7" d="M66.6,96.1c1.3-2.4,3.3-5.5,2.9-6.4c0,0,3.3,5,6.3,5.8c-0.1,0-0.3,0-0.3,0c-0.8,0.3-1,2.5-1.1,3.1
	C73.7,101.1,65.3,98.6,66.6,96.1z"/>
<path class="st4" d="M74.8,85.3c-3.7-0.6-1.4,7.6-5.4,7.3c-4-0.3-6.8-9.1-3.9-15.9c3-6.8,8.6-6.4,14-5.4s12,3.4,12,7.4
	c0,9.7-11.6,1.6-12.3,4.5c-0.6,2.3-1.5,8.8,0.9,8.9c2.4,0.1,4.8-1.8,6.5-1.3c1.1,0.3,4,13.1-3.7,13.5c-7.6,0.4-9.6-6.3-7.9-11.7
	C76.5,87.3,76.7,85.6,74.8,85.3z"/>
<path class="st0" d="M279.7,288.7c0,1.6,8.9,3,20,3s20-1.3,20-3s-8.9-3-20-3S279.7,286.9,279.7,288.7z"/>
<path class="st8" d="M326.8,199.2c0.8,3.5-3.5,4.2-4.2,9.1c-0.8,4.9,1.4,6.3,0.8,9.1c-0.8,2.8-4.9,3.5-5.5,10.6
	c-0.8,7,2.1,6.3,1.4,10.6c-0.8,4.2-4.9,4.2-5.5,7.7c-0.8,3.5-0.8,8.4-2.1,12.6c-1.4,4.2-5.5,10.6-7.7,10.6c-2.1,0-7.7-12-6.3-15.4
	c1.4-3.5,3.5-4.2,3.5-7c0-2.8-2.8-9.8-2.1-14.7c0.8-4.9,7-7,7.7-10.6c0.8-3.5-1.4-12.6,3.5-16.7C315,200.6,326.2,195.8,326.8,199.2z
	"/>
<path class="st9" d="M304,268c0.3,0,0.4-0.1,0.4-0.4c0-0.1,0.1-9.2,2.8-11.8c2.6-2.6,4.3-11,5-26.8c0.8-14.3,11.1-26.3,11.1-26.4
	c0.1-0.1,0.1-0.4,0-0.5c-0.1-0.1-0.4-0.1-0.5,0c-0.1,0.1-10.6,12.2-11.3,26.8c-0.8,15.5-2.3,23.8-4.8,26.4c-2.9,2.9-2.9,12-2.9,12.3
	C303.7,267.9,303.9,268,304,268z"/>
<path class="st8" d="M262.8,209.9c-2.5,2.5,5.5,3.5,6.3,8.4c0.8,4.9,0.8,7,3.5,9.8c2.8,2.8,7,3.5,6.3,7.7c-0.8,4.2-2.1,8.4,0,10.6
	c2.1,2.1,7,2.1,8.4,5.5c1.4,3.5,0.8,7.7,3.5,11.2s7,10.6,10.6,9.1c3.5-1.4,1.4-17.5,0-20.3c-1.4-2.8-2.8-4.9-2.8-7
	c0-2.1,0.8-5.5-0.8-9.1c-1.4-3.5-9.1-4.9-9.8-7s-1.4-13.3-5.5-16.1C278.1,209.9,266.9,205.6,262.8,209.9z"/>
<path class="st2" d="M300.1,270.9c0.1,0,0.1,0,0.3,0c0.1-0.1,0.3-0.3,0.1-0.5c0,0-2.9-4.8-3.4-7.2c-0.1-0.6-0.4-1.5-0.5-2.6
	c-0.8-3.9-1.9-9.7-3.8-11.6c-2.6-2.6-6.8-10.6-9.7-20.9c-3.7-12.6-15.5-16.4-15.6-16.4c-0.3,0-0.4,0-0.4,0.3c0,0.3,0,0.4,0.3,0.4
	c0.1,0,11.7,3.7,15.2,15.9c2.9,10.3,7.2,18.5,9.8,21.1c1.6,1.6,2.9,7.7,3.5,11.2c0.3,1.1,0.4,2,0.5,2.6c0.6,2.5,3.4,7,3.5,7.3
	C299.9,270.9,300,270.9,300.1,270.9z"/>
<path class="st6" d="M301,217.8c8.4,0.8,5.5,25.9,5.5,25.9c-2.1,21.8-4.9,31.5-7,32.2c-2.1,0.8-6.3-30.8-6.3-38.5
	C293.3,237.4,292.6,217.2,301,217.8z"/>
<path class="st2" d="M300,272.6L300,272.6c0.3,0,0.4-0.3,0.4-0.4c-2.8-22.8,0.6-51.5,0.8-51.7c0-0.3-0.1-0.4-0.3-0.4
	c-0.3,0-0.4,0.1-0.4,0.3c0,0.3-3.5,29.1-0.8,52C299.6,272.3,299.9,272.6,300,272.6z"/>
<path class="st0" d="M289.6,288.4H311c0.8,0,1.3-0.4,1.4-1l3-22.2c0.1-1-0.6-1.9-1.6-1.9h-27.1c-1,0-1.8,0.9-1.6,1.9l3,22.2
	C288.3,288,288.8,288.4,289.6,288.4z"/>
<circle class="st1" cx="319.4" cy="42.1" r="3.1"/>
<circle class="st1" cx="306.9" cy="42.1" r="3.1"/>
<circle class="st1" cx="294.4" cy="42.1" r="3.1"/>
<g>
	<text transform="matrix(1.6123 0.6431 -0.3705 0.9288 70.185 60.3273)" class="st5 st10 st11">?</text>
</g>
<g>
	<text transform="matrix(1.5033 -1.2729 0.6462 0.7632 68.2525 70.9493)" class="st5 st10 st12">?</text>
</g>
<g>
	<text transform="matrix(1.7333 9.542076e-02 -5.499613e-02 0.9985 132.5605 180.2934)" class="st5 st10 st13">?</text>
</g>
</svg>
