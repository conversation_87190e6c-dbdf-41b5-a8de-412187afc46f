{
  "env": {
    "browser": true,
    "es6": true,
    "node": true
  },
  "parser": "@typescript-eslint/parser",
  "react/function-component-definition": "off",
  "parserOptions": {
    "ecmaVersion": "latest",
    "sourceType": "module",
    "ecmaFeatures": {
      "jsx": true
    },
    "project": "./tsconfig.json"
  },
  "extends": [
    "airbnb",
    "plugin:react/recommended",
    "plugin:@typescript-eslint/recommended", 
    "plugin:prettier/recommended"
  ],
  "plugins": [
    "prettier",
    "unused-imports",
    "@typescript-eslint" // ✅ register plugin
  ],
  "settings": {
    "import/resolver": {
      "node": {
        "paths": ["src"],
        "extensions": [".js", ".jsx", ".ts", ".tsx"]
      }
    }
  },
  "rules": {
    "prettier/prettier": [
      "warn",
      {
        "arrowParens": "always",
        "bracketSpacing": true,
        "jsxBracketSameLine": false,
        "jsxSingleQuote": false,
        "printWidth": 100,
        "proseWrap": "always",
        "quoteProps": "as-needed",
        "semi": true,
        "singleQuote": true,
        "tabWidth": 2,
        "trailingComma": "es5",
        "useTabs": false,
        "endOfLine": "auto"
      }
    ],
    "react/jsx-filename-extension": [
      "warn",
      {
        "extensions": [".js", ".jsx", ".ts", ".tsx"]
      }
    ],
    "import/extensions": [
      "warn",
      "ignorePackages",
      {
        "js": "never",
        "jsx": "never",
        "ts": "never",
        "tsx": "never"
      }
    ],
    "import/no-extraneous-dependencies": ["warn", { "devDependencies": true }],
    "no-unused-vars": "off",
    "no-console": "off",
    "no-use-before-define": "off",
    "no-underscore-dangle": "off",
    "no-param-reassign": "off",
    "import/no-unresolved": "off",
    "global-require": "off",
    "react/no-array-index-key": "off",
    "react/no-unescaped-entities": "off",
    "react/destructuring-assignment": "off",
    "react/jsx-props-no-spreading": "off",
    "react/state-in-constructor": "off",
    "react/no-danger": "off",
    "react/prop-types": "off",
    "react/forbid-prop-types": "off",
    "react/require-default-props": "off",
    "react/default-props-match-prop-types": "off",
    "react/no-unused-prop-types": "off",
    "react/react-in-jsx-scope": "off",
    "react/jsx-uses-react": "off",
    "react/jsx-no-bind": "off",
    "unused-imports/no-unused-imports": "warn"
  }
}
