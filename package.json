{"name": "fuse-react-app", "version": "8.3.5", "private": true, "dependencies": {"@auth0/auth0-react": "^2.1.0", "@emotion/cache": "11.10.5", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@formio/react": "^5.3.0-rc.3", "@fortawesome/fontawesome-svg-core": "^6.5.1", "@fortawesome/free-solid-svg-icons": "^6.5.1", "@fortawesome/react-fontawesome": "^0.2.0", "@fullcalendar/core": "^6.1.13", "@fullcalendar/daygrid": "^6.1.13", "@fullcalendar/interaction": "^6.1.13", "@fullcalendar/react": "^6.1.13", "@fullcalendar/timegrid": "^6.1.13", "@hookform/resolvers": "^3.0.0", "@material-ui/icons": "^4.11.3", "@mui/base": "5.0.0-alpha.123", "@mui/icons-material": "^5.15.20", "@mui/joy": "^5.0.0-beta.36", "@mui/lab": "5.0.0-alpha.124", "@mui/material": "^5.15.20", "@mui/material-next": "6.0.0-alpha.79", "@mui/styles": "5.11.13", "@mui/system": "5.11.15", "@mui/utils": "5.11.13", "@mui/x-data-grid": "6.0.4", "@mui/x-date-pickers": "^6.0.4", "@reduxjs/toolkit": "1.9.3", "@syncfusion/ej2-react-schedule": "^25.2.4", "@tanstack/match-sorter-utils": "^8.19.4", "@tanstack/react-table": "^8.21.3", "@types/jest": "^29.5.1", "@types/node": "^20.2.3", "@types/react": "^18.2.7", "@types/react-beautiful-dnd": "^13.1.4", "@types/react-dom": "^18.2.4", "@types/uuid": "^10.0.0", "autosuggest-highlight": "3.3.4", "axios": "1.3.4", "axios-mock-adapter": "1.21.4", "chart.js": "^4.4.3", "clsx": "1.2.1", "core-js": "3.29.1", "crypto-js": "4.2.0", "date-fns": "^2.29.3", "dayjs": "^1.11.12", "docx": "^9.5.1", "draft-js": "0.11.7", "draftjs-to-html": "0.9.1", "emoji-picker-react": "^4.11.1", "formiojs": "^4.21.0", "framer-motion": "10.10.0", "history": "^5.3.0", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.2", "i18next": "22.4.14", "jwt-decode": "3.1.2", "keycode": "2.2.1", "lodash": "4.17.21", "marked": "4.3.0", "material-ui-popup-state": "5.0.8", "mobile-detect": "1.4.5", "mobx": "^6.9.0", "moment": "2.29.4", "notistack": "1.0.6-next.3", "npm": "^10.8.2", "perfect-scrollbar": "1.5.5", "pptxgenjs": "^4.0.0", "prismjs": "1.29.0", "prop-types": "15.8.1", "qs": "6.11.1", "react": "18.2.0", "react-autosuggest": "10.1.0", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.2.0", "react-dom": "18.2.0", "react-draft-wysiwyg": "1.15.0", "react-drag-drop-files": "^2.3.10", "react-formio": "^4.3.0", "react-helmet-async": "^1.3.0", "react-hook-form": "^7.43.9", "react-i18next": "12.2.0", "react-icons": "^5.3.0", "react-phone-number-input": "^3.4.8", "react-popper": "2.3.0", "react-quill": "^2.0.0", "react-redux": "8.0.5", "react-router-dom": "6.10.0", "react-swipeable": "7.0.0", "react-virtuoso": "^4.10.4", "react-window": "^1.8.10", "recharts": "^2.15.3", "redux-logger": "4.0.0", "redux-persist": "^6.0.0", "socket.io-client": "^4.7.4", "styled-components": "5.3.9", "stylis": "4.1.3", "stylis-plugin-rtl": "2.1.1", "uuid": "^11.1.0", "web-vitals": "3.3.0", "xlsx": "^0.18.5", "yup": "^1.0.2"}, "peerDependencies": {"autoprefixer": "10.4.7", "postcss": "8.4.20", "react": "18.2.0", "react-dom": "18.2.0"}, "resolutions": {"react": "18.2.0", "react-dom": "18.2.0", "babel-loader": "8.1.0"}, "devDependencies": {"@babel/core": "7.21.4", "@babel/eslint-parser": "7.21.3", "@babel/node": "7.20.7", "@babel/preset-env": "7.21.4", "@babel/preset-react": "7.18.6", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/typography": "^0.5.9", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "autoprefixer": "10.4.14", "cross-env": "7.0.3", "eslint": "8.37.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^8.8.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unused-imports": "^2.0.0", "immutable": "4.3.0", "postcss": "8.4.21", "prettier": "^2.8.7", "promise": "8.3.0", "react-app-alias": "2.2.2", "react-app-rewired": "2.2.1", "react-scripts": "5.0.1", "source-map-explorer": "2.5.3", "tailwindcss": "3.3.1", "typescript": "^5.0.3", "util": "^0.12.5"}, "scripts": {"start": "react-app-rewired  start", "build": "cross-env GENERATE_SOURCEMAP=false react-app-rewired build", "test": "react-app-rewired test --env=node -passWithNoTests", "eject": "react-app-rewired eject", "build-docs": "babel-node --presets @babel/preset-env src/app/main/documentation/material-ui-components/build.js", "analyze": "react-app-rewired build && source-map-explorer 'build/static/js/*.js' --html analyze-result.html", "lint": "eslint"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 3 safari version"]}}