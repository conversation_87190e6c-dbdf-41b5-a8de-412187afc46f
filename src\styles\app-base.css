/*@tailwind base;*/


/**
 * Custom base styles
 */

:root {
    --primaryColor: #5B718F;
    --pc1: rgb(91, 113, 143, 0.1);
    --pc3: rgb(91, 113, 143, 0.3);
    --pc5: rgb(91, 113, 143, 0.5);
    --pc7: rgb(91, 113, 143, 0.7);
    --pc9: rgb(91, 113, 143, 0.9);
}

* {
    /* Text rendering */
    text-rendering: optimizeLegibility;
    -o-text-rendering: optimizeLegibility;
    -ms-text-rendering: optimizeLegibility;
    -moz-text-rendering: optimizeLegibility;
    -webkit-text-rendering: optimizeLegibility;
    -webkit-tap-highlight-color: transparent;
}

* :focus {
    outline: none !important;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}


/* Firefox */

input[type=number] {
    -moz-appearance: textfield;
}

html {
    font-size: 62.5%;
    font-family: 'Inter var', Roboto, Helvetica Neue, Arial, sans-serif;
    background-color: #121212;
    scroll-behavior: smooth;
}

body {
    font-size: 14px;
    line-height: 1.4;
    overflow-x: hidden;
    font-feature-settings: "salt";
}

html,
body {
    -webkit-font-smoothing: auto;
    -moz-osx-font-smoothing: auto;
}

html,
body {
    display: flex;
    flex-direction: column;
    position: relative;
    margin: 0;
    min-height: 100%;
    width: 100%;
    flex: 1 1 auto;
}

#root {
    display: flex;
    flex: 1 1 auto;
    width: 100%;
    height: 100%;
}

h1,
.h1 {
    font-size: 24px;
}

h2,
.h2 {
    font-size: 20px;
}

h3,
.h3 {
    font-size: 16px;
}

h4,
.h4 {
    font-size: 15px;
}

h5,
.h5 {
    font-size: 13px;
}

h6,
.h6 {
    font-size: 12px;
}

.ps>.ps__rail-y,
.ps>.ps__rail-x {
    z-index: 99;
}

a[role=button] {
    text-decoration: none;
}

[role="tooltip"] {
    z-index: 9999;
}

.MuiModal-root {
    /*z-index: 9999;*/
}


/* Medium Devices, Desktops Only */

@media only screen and (min-width: 992px) {
    ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
        background-color: rgba(0, 0, 0, 0);
    }

    ::-webkit-scrollbar:hover {
        width: 8px;
        height: 8px;
        background-color: rgba(0, 0, 0, 0.06);
    }

    ::-webkit-scrollbar-thumb {
        border: 2px solid transparent;
        border-radius: 20px;
    }

    ::-webkit-scrollbar-thumb:active {
        border-radius: 20px;
    }
}

form label {
    z-index: 99;
}

body.no-animate *,
body.no-animate *::before,
body.no-animate *::after {
    transition: none !important;
    animation: none !important;
}


button:focus {
    outline: none;
}


/* Removes webkit's autofill backgorund color */

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    transitionDelay: 9999s;
    transitionProperty: background-color, color;
}

:focus {
    outline-color: transparent;
}


/*fullcalendar Fix*/

.fc-scrollgrid-section-liquid {
    height: 1px !important;
}

.goog-te-combo {
    color: #808080;
    border-radius: 4px;
    /* padding: 9px 10px; */
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    margin: 0 !important;
    color: black;
}

#google_translate_element {
    width: 100%;
    height: 38px;
    position: relative;
}

.VIpgJd-ZVi9od-l4eHX-hSRGPd {
    display: none;
}

.goog-te-gadget img {
    display: none !important;
}

body>.skiptranslate {
    display: none;
}

body {
    top: 0px !important;
}

#google_translate_element {
    max-width: 200px;
    width: 100%;
    float: right;
    text-align: right;
    display: block;
}

.goog-te-banner-frame.skiptranslate {
    display: none !important;
}

body {
    top: 0px !important;
}

#goog-gt-tt {
    display: none !important;
    top: 0px !important;
}

.goog-tooltip skiptranslate {
    display: none !important;
    top: 0px !important;
}

.activity-root {
    display: hide !important;
}

.status-message {
    display: hide !important;
}

.started-activity-container {
    display: hide !important;
}

.VIpgJd-ZVi9od-aZ2wEe-wOHMyf.VIpgJd-ZVi9od-aZ2wEe-wOHMyf-ti6hGc {
    display: none;
}