.container {
  border-radius: 8px;
  background-color: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.header {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
}

.tableContainer {
  margin-bottom: 16px;
  overflow-x: auto;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
  box-shadow: none;
}

.addButton {
  margin-top: 8px;
  margin-bottom: 8px;
  border-radius: 4px;
}

.actionButtons {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.bottomActions {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

/* Table styles */
.tableContainer table {
  min-width: 1200px;
  border-collapse: collapse;
  width: 100%;
}

.tableContainer th {
  font-weight: bold;
  background-color: #f8f8f8;
  padding: 10px 8px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
  font-size: 12px;
}

.tableContainer td {
  padding: 8px;
  border-bottom: 1px solid #f0f0f0;
  vertical-align: middle;
}

/* Column widths */
.typeColumn {
  width: 120px;
}

.titleColumn {
  width: 200px;
}

.codeColumn {
  width: 80px;
}

.orderColumn {
  width: 80px;
}

.descriptionColumn {
  width: 250px;
}

.methodColumn {
  width: 40px;
  text-align: center;
}

.timesMetColumn {
  width: 80px;
}

.actionsColumn {
  width: 120px;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .tableContainer {
    max-width: 100%;
    overflow-x: auto;
  }
}
